import json
import pandas as pd

# 查看工艺优化报告
print("=== 工艺优化报告示例 ===")
with open('工艺优化报告.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print(f"共生成{len(data)}炉次的工艺优化报告")
print("\n第1炉次报告示例:")
print(json.dumps(data[0], ensure_ascii=False, indent=2))

# 查看预测结果统计
print("\n=== 预测结果统计 ===")
df_stats = pd.read_excel('炉渣成分预测结果_20250526_0858.xlsx', sheet_name='预测精度统计')
print("温度预测精度:")
print(f"- 样本数量: {df_stats.iloc[0]['样本数量']:.0f}")
print(f"- 平均绝对误差: {df_stats.iloc[0]['平均绝对误差']:.1f}°C")
print(f"- 标准偏差: {df_stats.iloc[0]['标准偏差']:.1f}°C")
print(f"- ±20°C精度: {df_stats.iloc[0]['精度±20°C']:.1f}%")

print("\n炉渣成分统计:")
print(f"- CaO平均值: {df_stats.iloc[1]['CaO平均值']:.1f}%")
print(f"- SiO2平均值: {df_stats.iloc[1]['SiO2平均值']:.1f}%")
print(f"- FeO平均值: {df_stats.iloc[1]['FeO平均值']:.1f}%")
print(f"- 平均碱度: {df_stats.iloc[1]['平均碱度']:.1f}")

print("\n过热度分析:")
print(f"- 平均过热度: {df_stats.iloc[2]['平均过热度']:.1f}°C")
print(f"- 适宜过热度比例: {df_stats.iloc[2]['适宜过热度比例']:.1f}%")
