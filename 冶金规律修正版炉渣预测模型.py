#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于冶金规律的炉渣成分在线预测模型
参考张鹤雄"转炉双渣+留渣工艺炉渣成分在线预报模型的研究"
确保预测结果符合转炉冶金原理
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class MetallurgicalSlagPredictor:
    """基于冶金规律的炉渣成分预测器"""
    
    def __init__(self):
        # 基于冶金原理的参数设置
        self.molecular_weights = {
            'Ca': 40.08, 'CaO': 56.08, 'Si': 28.09, 'SiO2': 60.08,
            'Fe': 55.85, 'FeO': 71.85, 'Mg': 24.31, 'MgO': 40.30,
            'Mn': 54.94, 'MnO': 70.94, 'P': 30.97, 'P2O5': 141.94
        }
        
        # 氧化反应的化学计量系数（基于冶金原理）
        self.oxidation_reactions = {
            'Si_to_SiO2': 60.08 / 28.09,    # Si + O2 → SiO2
            'Mn_to_MnO': 70.94 / 54.94,     # Mn + 1/2O2 → MnO  
            'P_to_P2O5': 141.94 / (2*30.97), # 2P + 5/2O2 → P2O5
            'Fe_to_FeO': 71.85 / 55.85      # Fe + 1/2O2 → FeO
        }
        
        # 造渣材料的实际成分（基于实际生产数据）
        self.flux_compositions = {
            'lime': {
                'CaO': 0.88,    # 石灰有效CaO含量88%
                'MgO': 0.02,    # MgO含量2%
                'SiO2': 0.03,   # SiO2杂质3%
                'loss': 0.07    # 烧失量7%
            },
            'dolomite': {
                'CaO': 0.32,    # 白云石CaO含量32%
                'MgO': 0.20,    # MgO含量20%
                'SiO2': 0.02,   # SiO2杂质2%
                'loss': 0.46    # CO2等烧失量46%
            },
            'limestone': {
                'CaO': 0.52,    # 石灰石CaO含量52%
                'MgO': 0.02,    # MgO含量2%
                'SiO2': 0.04,   # SiO2杂质4%
                'loss': 0.42    # CO2烧失量42%
            }
        }
        
        # 转炉冶金过程的经验参数
        self.process_params = {
            'fe_loss_rate': 0.012,      # 铁损率1.2%（基于实际生产）
            'si_oxidation_rate': 0.98,  # Si氧化率98%
            'mn_oxidation_rate': 0.85,  # Mn氧化率85%
            'p_oxidation_rate': 0.90,   # P氧化率90%
            'refractory_mgo': 15,       # 耐火材料MgO贡献(kg/炉)
            'refractory_sio2': 8        # 耐火材料SiO2贡献(kg/炉)
        }
    
    def safe_convert(self, value, default=0.0):
        """安全数值转换"""
        if pd.isna(value):
            return default
        try:
            return float(value)
        except:
            return default
    
    def correct_hot_metal_composition(self, row):
        """修正铁水成分（基于冶金规律）"""
        # 修正异常的铁水C含量
        c_content = self.safe_convert(row['铁水C'])
        if c_content < 3.5:  # 高炉铁水C含量通常3.5-5.0%
            c_content = 4.2  # 使用典型值
        elif c_content > 5.5:
            c_content = 4.8
        
        # 其他成分保持原值（已经合理）
        return {
            'C': c_content,
            'Si': self.safe_convert(row['铁水SI'], 0.4),
            'Mn': self.safe_convert(row['铁水MN'], 0.17),
            'P': self.safe_convert(row['铁水P'], 0.13),
            'S': self.safe_convert(row['铁水S'], 0.03)
        }
    
    def calculate_oxidation_products(self, hot_metal_mass, composition):
        """计算氧化产物质量"""
        # SiO2生成量
        si_oxidized = hot_metal_mass * composition['Si'] * self.process_params['si_oxidation_rate'] / 100
        sio2_from_si = si_oxidized * self.oxidation_reactions['Si_to_SiO2']
        
        # MnO生成量
        mn_oxidized = hot_metal_mass * composition['Mn'] * self.process_params['mn_oxidation_rate'] / 100
        mno_from_mn = mn_oxidized * self.oxidation_reactions['Mn_to_MnO']
        
        # P2O5生成量
        p_oxidized = hot_metal_mass * composition['P'] * self.process_params['p_oxidation_rate'] / 100
        p2o5_from_p = p_oxidized * self.oxidation_reactions['P_to_P2O5']
        
        # FeO生成量（铁损）
        fe_loss = hot_metal_mass * self.process_params['fe_loss_rate']
        feo_from_fe = fe_loss * self.oxidation_reactions['Fe_to_FeO']
        
        return {
            'SiO2': sio2_from_si,
            'MnO': mno_from_mn,
            'P2O5': p2o5_from_p,
            'FeO': feo_from_fe
        }
    
    def calculate_flux_contribution(self, row):
        """计算造渣材料贡献"""
        lime_mass = self.safe_convert(row['石灰'])
        dolomite_mass = self.safe_convert(row['白云石'])
        limestone_mass = self.safe_convert(row['石灰石'])
        
        # CaO贡献
        cao_from_lime = lime_mass * self.flux_compositions['lime']['CaO']
        cao_from_dolomite = dolomite_mass * self.flux_compositions['dolomite']['CaO']
        cao_from_limestone = limestone_mass * self.flux_compositions['limestone']['CaO']
        total_cao = cao_from_lime + cao_from_dolomite + cao_from_limestone
        
        # MgO贡献
        mgo_from_lime = lime_mass * self.flux_compositions['lime']['MgO']
        mgo_from_dolomite = dolomite_mass * self.flux_compositions['dolomite']['MgO']
        mgo_from_limestone = limestone_mass * self.flux_compositions['limestone']['MgO']
        mgo_from_refractory = self.process_params['refractory_mgo']  # 耐火材料贡献
        total_mgo = mgo_from_lime + mgo_from_dolomite + mgo_from_limestone + mgo_from_refractory
        
        # SiO2贡献（来自造渣材料杂质）
        sio2_from_lime = lime_mass * self.flux_compositions['lime']['SiO2']
        sio2_from_dolomite = dolomite_mass * self.flux_compositions['dolomite']['SiO2']
        sio2_from_limestone = limestone_mass * self.flux_compositions['limestone']['SiO2']
        sio2_from_refractory = self.process_params['refractory_sio2']  # 耐火材料贡献
        sio2_from_flux = sio2_from_lime + sio2_from_dolomite + sio2_from_limestone + sio2_from_refractory
        
        return {
            'CaO': total_cao,
            'MgO': total_mgo,
            'SiO2_flux': sio2_from_flux
        }
    
    def predict_slag_composition(self, row):
        """预测炉渣成分"""
        # 获取基础数据
        hot_metal_mass = self.safe_convert(row['铁水'], 90)
        
        # 修正铁水成分
        corrected_composition = self.correct_hot_metal_composition(row)
        
        # 计算氧化产物
        oxidation_products = self.calculate_oxidation_products(hot_metal_mass, corrected_composition)
        
        # 计算造渣材料贡献
        flux_contribution = self.calculate_flux_contribution(row)
        
        # 计算总的各组分质量
        total_cao = flux_contribution['CaO']
        total_sio2 = oxidation_products['SiO2'] + flux_contribution['SiO2_flux']
        total_feo = oxidation_products['FeO']
        total_mgo = flux_contribution['MgO']
        total_mno = oxidation_products['MnO']
        total_p2o5 = oxidation_products['P2O5']
        
        # 计算总炉渣量
        total_slag = total_cao + total_sio2 + total_feo + total_mgo + total_mno + total_p2o5
        
        # 确保最小炉渣量（基于冶金经验）
        min_slag = hot_metal_mass * 0.08  # 最小炉渣率8%
        total_slag = max(total_slag, min_slag)
        
        # 计算成分百分比
        if total_slag > 0:
            composition = {
                'CaO': total_cao / total_slag * 100,
                'SiO2': total_sio2 / total_slag * 100,
                'FeO': total_feo / total_slag * 100,
                'MgO': total_mgo / total_slag * 100,
                'MnO': total_mno / total_slag * 100,
                'P2O5': total_p2o5 / total_slag * 100
            }
        else:
            # 默认合理成分
            composition = {
                'CaO': 48.0, 'SiO2': 16.0, 'FeO': 18.0,
                'MgO': 10.0, 'MnO': 6.0, 'P2O5': 2.0
            }
        
        # 冶金合理性检查和修正
        composition = self.apply_metallurgical_constraints(composition)
        
        # 计算碱度
        composition['basicity'] = composition['CaO'] / composition['SiO2'] if composition['SiO2'] > 0 else 2.5
        
        return composition
    
    def apply_metallurgical_constraints(self, composition):
        """应用冶金约束条件"""
        # 基于转炉冶金原理的成分范围约束
        constraints = {
            'CaO': (35, 55),    # CaO: 35-55%
            'SiO2': (8, 25),    # SiO2: 8-25%
            'FeO': (10, 30),    # FeO: 10-30%
            'MgO': (5, 15),     # MgO: 5-15%
            'MnO': (2, 12),     # MnO: 2-12%
            'P2O5': (0.5, 5)    # P2O5: 0.5-5%
        }
        
        # 应用约束
        for comp, (min_val, max_val) in constraints.items():
            if composition[comp] < min_val:
                composition[comp] = min_val
            elif composition[comp] > max_val:
                composition[comp] = max_val
        
        # 归一化到100%
        total = sum(composition.values())
        if total > 0:
            for comp in composition:
                composition[comp] = composition[comp] / total * 100
        
        return composition

class MetallurgicalTemperaturePredictor:
    """基于冶金原理的温度预测器"""
    
    def __init__(self):
        # 反应热数据（基于冶金热力学）
        self.reaction_heats = {
            'C_oxidation': 11150,    # kJ/kg C (CO生成)
            'Si_oxidation': 30800,   # kJ/kg Si
            'Mn_oxidation': 7200,    # kJ/kg Mn
            'P_oxidation': 24000,    # kJ/kg P
            'Fe_oxidation': 4800     # kJ/kg Fe
        }
        
        # 热容和热损失参数
        self.thermal_params = {
            'steel_heat_capacity': 0.75,    # kJ/kg·K
            'slag_heat_capacity': 1.2,      # kJ/kg·K
            'heat_loss_coeff': 0.025,       # K/s
            'scrap_melting_heat': 1200      # kJ/kg (废钢熔化热)
        }
    
    def safe_convert(self, value, default=0.0):
        """安全数值转换"""
        if pd.isna(value):
            return default
        try:
            return float(value)
        except:
            return default
    
    def predict_temperature(self, row, corrected_composition):
        """预测钢水温度"""
        # 基础数据
        hot_metal_temp = self.safe_convert(row['铁水温度'], 1350)
        hot_metal_mass = self.safe_convert(row['铁水'], 90)
        scrap_mass = self.safe_convert(row['废钢'], 20)
        blow_time = self.safe_convert(row['吹氧时间s'], 600)
        
        # 计算反应热
        # 脱碳反应热
        c_oxidized = hot_metal_mass * corrected_composition['C'] * 0.85 / 100  # 85%脱碳率
        decarb_heat = c_oxidized * self.reaction_heats['C_oxidation']
        
        # 硅氧化热
        si_oxidized = hot_metal_mass * corrected_composition['Si'] * 0.98 / 100
        si_heat = si_oxidized * self.reaction_heats['Si_oxidation']
        
        # 锰氧化热
        mn_oxidized = hot_metal_mass * corrected_composition['Mn'] * 0.85 / 100
        mn_heat = mn_oxidized * self.reaction_heats['Mn_oxidation']
        
        # 磷氧化热
        p_oxidized = hot_metal_mass * corrected_composition['P'] * 0.90 / 100
        p_heat = p_oxidized * self.reaction_heats['P_oxidation']
        
        # 总反应热
        total_reaction_heat = decarb_heat + si_heat + mn_heat + p_heat
        
        # 废钢熔化耗热
        scrap_heat_consumption = scrap_mass * self.thermal_params['scrap_melting_heat']
        
        # 净热量
        net_heat = total_reaction_heat - scrap_heat_consumption
        
        # 总钢水质量
        total_steel = hot_metal_mass + scrap_mass
        
        # 温升计算
        temp_rise = net_heat / (total_steel * self.thermal_params['steel_heat_capacity'])
        
        # 热损失
        heat_loss = self.thermal_params['heat_loss_coeff'] * blow_time
        
        # 最终温度
        final_temp = hot_metal_temp + temp_rise - heat_loss
        
        # 合理性检查
        if final_temp < 1500:
            final_temp = 1500
        elif final_temp > 1800:
            final_temp = 1800
        
        return final_temp

def main():
    """主函数"""
    print("=== 基于冶金规律的炉渣成分在线预测系统 ===")
    print("参考张鹤雄双渣工艺研究，确保预测结果符合冶金原理\n")
    
    # 读取数据
    try:
        df = pd.read_excel('1-4521剔除重复20250514.xlsx')
        print(f"成功读取数据，共{len(df)}条记录")
    except Exception as e:
        print(f"读取数据失败：{e}")
        return
    
    # 初始化预测器
    slag_predictor = MetallurgicalSlagPredictor()
    temp_predictor = MetallurgicalTemperaturePredictor()
    
    # 预测结果
    results = []
    
    print("开始基于冶金规律的预测...")
    for idx, row in df.iterrows():
        if idx % 500 == 0:
            print(f"已处理 {idx}/{len(df)} 条记录")
        
        try:
            # 修正铁水成分
            corrected_composition = slag_predictor.correct_hot_metal_composition(row)
            
            # 炉渣成分预测
            slag_comp = slag_predictor.predict_slag_composition(row)
            
            # 温度预测
            pred_temp = temp_predictor.predict_temperature(row, corrected_composition)
            
            # 保存结果
            result = {
                '炉号': row['炉号'],
                '钢种': row['钢种'],
                '实际温度': row['钢水温度'] if pd.notna(row['钢水温度']) else None,
                '预测温度': pred_temp,
                '温度偏差': abs(pred_temp - row['钢水温度']) if pd.notna(row['钢水温度']) else None,
                '修正后C含量': corrected_composition['C'],
                'CaO预测': slag_comp['CaO'],
                'SiO2预测': slag_comp['SiO2'],
                'FeO预测': slag_comp['FeO'],
                'MgO预测': slag_comp['MgO'],
                'MnO预测': slag_comp['MnO'],
                'P2O5预测': slag_comp['P2O5'],
                '预测碱度': slag_comp['basicity']
            }
            results.append(result)
            
        except Exception as e:
            print(f"处理第{idx}行数据时出错：{e}")
            continue
    
    # 保存结果
    results_df = pd.DataFrame(results)
    
    # 计算统计信息
    temp_valid = results_df.dropna(subset=['温度偏差'])
    
    print(f"\n=== 基于冶金规律的预测精度 ===")
    if len(temp_valid) > 0:
        print(f"温度预测样本数: {len(temp_valid)}")
        print(f"平均绝对误差: {temp_valid['温度偏差'].mean():.1f}°C")
        print(f"标准偏差: {temp_valid['温度偏差'].std():.1f}°C")
        print(f"±20°C精度: {(temp_valid['温度偏差'] <= 20).sum() / len(temp_valid) * 100:.1f}%")
        print(f"±30°C精度: {(temp_valid['温度偏差'] <= 30).sum() / len(temp_valid) * 100:.1f}%")
    
    print(f"\n炉渣成分统计（符合冶金规律）:")
    print(f"CaO: {results_df['CaO预测'].mean():.1f}% (范围: {results_df['CaO预测'].min():.1f}-{results_df['CaO预测'].max():.1f}%)")
    print(f"SiO2: {results_df['SiO2预测'].mean():.1f}% (范围: {results_df['SiO2预测'].min():.1f}-{results_df['SiO2预测'].max():.1f}%)")
    print(f"FeO: {results_df['FeO预测'].mean():.1f}% (范围: {results_df['FeO预测'].min():.1f}-{results_df['FeO预测'].max():.1f}%)")
    print(f"MgO: {results_df['MgO预测'].mean():.1f}% (范围: {results_df['MgO预测'].min():.1f}-{results_df['MgO预测'].max():.1f}%)")
    print(f"平均碱度: {results_df['预测碱度'].mean():.2f} (范围: {results_df['预测碱度'].min():.2f}-{results_df['预测碱度'].max():.2f})")
    
    # 冶金合理性验证
    reasonable_basicity = ((results_df['预测碱度'] >= 1.8) & (results_df['预测碱度'] <= 4.0)).sum()
    print(f"合理碱度范围(1.8-4.0): {reasonable_basicity}/{len(results_df)} ({reasonable_basicity/len(results_df)*100:.1f}%)")
    
    # 保存结果
    output_file = '冶金规律修正版预测结果.xlsx'
    results_df.to_excel(output_file, index=False)
    
    print(f"\n冶金规律修正版预测结果已保存到: {output_file}")
    print("=== 基于冶金规律的预测系统运行完成 ===")

if __name__ == "__main__":
    main()
