import pandas as pd
import numpy as np

# 读取数据
df = pd.read_excel('1-4521剔除重复20250514.xlsx')

print("=== 数据质量分析 ===")
print(f"总记录数: {len(df)}")

# 数据类型分析
print("\n数据类型:")
for col in df.columns:
    print(f"{col}: {df[col].dtype}")

# 关键字段的数据范围分析
key_fields = ['铁水', '废钢', '铁水SI', '铁水MN', '铁水P', '铁水C', '石灰', '白云石', '钢水温度']

print("\n=== 关键字段统计 ===")
for field in key_fields:
    if field in df.columns:
        # 转换为数值类型
        df[field] = pd.to_numeric(df[field], errors='coerce')
        
        valid_data = df[field].dropna()
        if len(valid_data) > 0:
            print(f"\n{field}:")
            print(f"  有效数据: {len(valid_data)}/{len(df)} ({len(valid_data)/len(df)*100:.1f}%)")
            print(f"  范围: {valid_data.min():.3f} - {valid_data.max():.3f}")
            print(f"  平均值: {valid_data.mean():.3f}")
            print(f"  中位数: {valid_data.median():.3f}")
        else:
            print(f"{field}: 无有效数据")

# 分析典型炉次的数据
print("\n=== 典型炉次数据示例 ===")
sample_data = df.head(5)
for idx, row in sample_data.iterrows():
    print(f"\n炉次 {idx+1} (炉号: {row['炉号']}):")
    print(f"  铁水: {row['铁水']}t, 废钢: {row['废钢']}t")
    print(f"  铁水成分 - C: {row['铁水C']}%, Si: {row['铁水SI']}%, Mn: {row['铁水MN']}%, P: {row['铁水P']}%")
    print(f"  造渣料 - 石灰: {row['石灰']}kg, 白云石: {row['白云石']}kg")
    print(f"  钢水温度: {row['钢水温度']}°C")

# 基于冶金原理的合理性检查
print("\n=== 冶金原理合理性检查 ===")

# 检查铁水成分是否合理
si_valid = df[(df['铁水SI'] >= 0.1) & (df['铁水SI'] <= 1.5)]
print(f"Si含量合理范围(0.1-1.5%): {len(si_valid)}/{len(df)} ({len(si_valid)/len(df)*100:.1f}%)")

c_valid = df[(df['铁水C'] >= 3.5) & (df['铁水C'] <= 5.0)]
print(f"C含量合理范围(3.5-5.0%): {len(c_valid)}/{len(df)} ({len(c_valid)/len(df)*100:.1f}%)")

# 检查造渣料用量是否合理
lime_reasonable = df[(df['石灰'] >= 500) & (df['石灰'] <= 8000)]
print(f"石灰用量合理范围(500-8000kg): {len(lime_reasonable)}/{len(df)} ({len(lime_reasonable)/len(df)*100:.1f}%)")

# 检查温度是否合理
temp_reasonable = df[(df['钢水温度'] >= 1550) & (df['钢水温度'] <= 1700)]
print(f"钢水温度合理范围(1550-1700°C): {len(temp_reasonable)}/{len(df)} ({len(temp_reasonable)/len(df)*100:.1f}%)")

print("\n=== 数据分析完成 ===")
