#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于FactSage活度计算的高精度集成模型
结合双渣法脱磷理论、供氧模型、底吹模型、动态控制
进一步提高终点温度命中率和泛化性
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
import warnings
warnings.filterwarnings('ignore')

class FactSageIntegratedModel:
    """基于FactSage的集成优化模型"""
    
    def __init__(self):
        # FactSage活度计算参数（1600°C基准）
        self.factsage_activity = {
            # Wagner相互作用参数（用于活度计算）
            'e_C_C': 0.14,          # 碳的自相互作用参数
            'e_C_Si': 0.08,         # 碳-硅相互作用参数
            'e_Si_Si': 0.11,        # 硅的自相互作用参数
            'e_Mn_C': -0.012,       # 锰-碳相互作用参数
            'e_P_C': 0.062,         # 磷-碳相互作用参数
            'e_P_Si': 0.077,        # 磷-硅相互作用参数
            'e_O_C': -0.45,         # 氧-碳相互作用参数
            'e_O_Si': -0.131,       # 氧-硅相互作用参数
            'e_O_P': -0.070         # 氧-磷相互作用参数
        }
        
        # FactSage热力学数据（温度相关）
        self.factsage_thermo = {
            # 标准生成焓（kJ/mol，1600°C）
            'delta_H_CO': -110.5,       # C + 1/2O2 → CO
            'delta_H_CO2': -393.5,      # C + O2 → CO2
            'delta_H_SiO2': -910.7,     # Si + O2 → SiO2
            'delta_H_MnO': -385.2,      # Mn + 1/2O2 → MnO
            'delta_H_P2O5': -1640.1,    # 2P + 5/2O2 → P2O5
            'delta_H_FeO': -272.0,      # Fe + 1/2O2 → FeO
            
            # 标准熵（J/mol·K，1600°C）
            'delta_S_CO': 197.7,
            'delta_S_CO2': 213.8,
            'delta_S_SiO2': 41.5,
            'delta_S_MnO': 59.7,
            'delta_S_P2O5': 228.9,
            'delta_S_FeO': 60.8,
            
            # 热容（J/mol·K）
            'Cp_steel': 46.0,
            'Cp_slag': 85.0,
            'Cp_gas': 29.0
        }
        
        # 双渣法脱磷优化参数
        self.dephosphorization = {
            # 脱磷反应平衡常数参数
            'A_deP': 22350,         # K
            'B_deP': -16.94,        # 无量纲
            
            # 最佳碱度计算参数
            'optimal_basicity_base': 2.8,
            'basicity_temp_coeff': 0.001,
            'basicity_p_coeff': 15.0,
            
            # 氧势控制参数
            'log_pO2_optimal': -8.5,    # 最佳氧势（atm）
            'pO2_temp_coeff': 0.002,
            'pO2_feo_coeff': 0.1
        }
        
        # 供氧模型参数
        self.oxygen_supply = {
            'base_intensity': 500,      # 基础供氧强度 Nm³/h
            'temp_response_coeff': 0.8, # 温度响应系数
            'decarb_demand_coeff': 12,  # 脱碳需氧系数
            'si_demand_coeff': 2.3,     # 硅氧化需氧系数
            'mn_demand_coeff': 0.8,     # 锰氧化需氧系数
            'p_demand_coeff': 2.5       # 磷氧化需氧系数
        }
        
        # 底吹模型参数
        self.bottom_blowing = {
            'stirring_efficiency': 0.85,   # 搅拌效率
            'mass_transfer_coeff': 0.12,   # 传质系数
            'reaction_rate_enhance': 1.25, # 反应速率增强因子
            'heat_transfer_coeff': 0.08    # 传热系数
        }
        
        # 动态控制参数
        self.dynamic_control = {
            'feedback_gain': 0.3,          # 反馈增益
            'prediction_horizon': 60,      # 预测时域（秒）
            'control_interval': 10,        # 控制间隔（秒）
            'temp_tolerance': 15           # 温度容差（°C）
        }
    
    def calculate_activity_coefficients(self, composition):
        """基于Wagner模型计算活度系数"""
        # 组分摩尔分数
        x_C = composition['C'] / 100
        x_Si = composition['Si'] / 100
        x_Mn = composition['Mn'] / 100
        x_P = composition['P'] / 100
        
        # 计算活度系数（Wagner模型）
        ln_gamma_C = (self.factsage_activity['e_C_C'] * x_C +
                     self.factsage_activity['e_C_Si'] * x_Si +
                     self.factsage_activity['e_Mn_C'] * x_Mn)
        
        ln_gamma_Si = (self.factsage_activity['e_Si_Si'] * x_Si +
                      self.factsage_activity['e_C_Si'] * x_C)
        
        ln_gamma_P = (self.factsage_activity['e_P_C'] * x_C +
                     self.factsage_activity['e_P_Si'] * x_Si)
        
        return {
            'gamma_C': np.exp(ln_gamma_C),
            'gamma_Si': np.exp(ln_gamma_Si),
            'gamma_P': np.exp(ln_gamma_P)
        }
    
    def calculate_oxygen_potential(self, feo_pct, temp_k):
        """计算氧势"""
        # 基于FeO平衡计算氧势
        # Fe + 1/2O2 = FeO
        # log(pO2) = 2*log(a_FeO) - 2*log(a_Fe) + 2*ΔG°/(2.303*R*T)
        
        delta_G = self.factsage_thermo['delta_H_FeO'] - temp_k * self.factsage_thermo['delta_S_FeO'] / 1000
        
        # 假设a_Fe = 1, a_FeO ≈ X_FeO
        log_pO2 = 2 * np.log10(feo_pct / 100) + 2 * delta_G / (2.303 * 8.314 * temp_k)
        
        return log_pO2
    
    def optimize_dephosphorization(self, composition, temp_k, target_p_removal=90):
        """优化脱磷工艺参数"""
        # 计算脱磷反应平衡常数
        log_K = self.dephosphorization['A_deP'] / temp_k + self.dephosphorization['B_deP']
        K_eq = 10 ** log_K
        
        # 最佳碱度计算
        optimal_basicity = (self.dephosphorization['optimal_basicity_base'] +
                           self.dephosphorization['basicity_temp_coeff'] * (temp_k - 1873) +
                           self.dephosphorization['basicity_p_coeff'] * composition['P'])
        
        # 最佳氧势计算
        optimal_log_pO2 = (self.dephosphorization['log_pO2_optimal'] +
                           self.dephosphorization['pO2_temp_coeff'] * (temp_k - 1873))
        
        # 脱磷效率预测
        activity_coeffs = self.calculate_activity_coefficients(composition)
        dephosphorization_efficiency = min(100, K_eq * optimal_basicity**2.5 * 
                                         activity_coeffs['gamma_P'] * 10**(optimal_log_pO2/2))
        
        return {
            'optimal_basicity': optimal_basicity,
            'optimal_log_pO2': optimal_log_pO2,
            'predicted_efficiency': dephosphorization_efficiency,
            'equilibrium_constant': K_eq
        }
    
    def calculate_dynamic_oxygen_demand(self, composition, temp_k, blow_time):
        """动态计算供氧需求"""
        # 基础需氧量计算
        decarb_demand = composition['C'] * self.oxygen_supply['decarb_demand_coeff']
        si_demand = composition['Si'] * self.oxygen_supply['si_demand_coeff']
        mn_demand = composition['Mn'] * self.oxygen_supply['mn_demand_coeff']
        p_demand = composition['P'] * self.oxygen_supply['p_demand_coeff']
        
        total_demand = decarb_demand + si_demand + mn_demand + p_demand
        
        # 温度修正
        temp_correction = 1 + self.oxygen_supply['temp_response_coeff'] * (temp_k - 1873) / 100
        
        # 时间相关的供氧强度
        time_factor = 1 - 0.3 * (blow_time / 600)  # 随时间递减
        
        dynamic_intensity = (self.oxygen_supply['base_intensity'] + total_demand) * temp_correction * time_factor
        
        return max(dynamic_intensity, 300)  # 最小供氧强度
    
    def model_bottom_blowing_effects(self, composition, oxygen_intensity):
        """模拟底吹效应"""
        # 搅拌强化传质
        mass_transfer_enhancement = (1 + self.bottom_blowing['mass_transfer_coeff'] * 
                                   np.sqrt(oxygen_intensity / 500))
        
        # 反应速率增强
        reaction_rate_factor = (self.bottom_blowing['reaction_rate_enhance'] * 
                              mass_transfer_enhancement)
        
        # 热传递增强
        heat_transfer_factor = (1 + self.bottom_blowing['heat_transfer_coeff'] * 
                              mass_transfer_enhancement)
        
        return {
            'mass_transfer_enhancement': mass_transfer_enhancement,
            'reaction_rate_factor': reaction_rate_factor,
            'heat_transfer_factor': heat_transfer_factor,
            'stirring_efficiency': self.bottom_blowing['stirring_efficiency']
        }
    
    def predict_temperature_with_factsage(self, row):
        """基于FactSage的高精度温度预测"""
        # 基础数据提取
        hot_metal_temp = self.safe_convert(row['铁水温度'], 1350)
        hot_metal_mass = self.safe_convert(row['铁水'], 90)
        scrap_mass = self.safe_convert(row['废钢'], 20)
        blow_time = self.safe_convert(row['吹氧时间s'], 600)
        
        # 修正铁水成分
        composition = {
            'C': max(self.safe_convert(row['铁水C'], 4.2), 3.5),
            'Si': self.safe_convert(row['铁水SI'], 0.4),
            'Mn': self.safe_convert(row['铁水MN'], 0.17),
            'P': self.safe_convert(row['铁水P'], 0.13),
            'S': self.safe_convert(row['铁水S'], 0.03)
        }
        
        temp_k = hot_metal_temp + 273.15
        
        # 1. 基于FactSage计算反应热
        activity_coeffs = self.calculate_activity_coefficients(composition)
        
        # 考虑活度系数的反应热计算
        c_heat = (hot_metal_mass * composition['C'] * 0.85 / 100 * 
                 abs(self.factsage_thermo['delta_H_CO']) * activity_coeffs['gamma_C'])
        
        si_heat = (hot_metal_mass * composition['Si'] * 0.95 / 100 * 
                  abs(self.factsage_thermo['delta_H_SiO2']) * activity_coeffs['gamma_Si'])
        
        mn_heat = hot_metal_mass * composition['Mn'] * 0.80 / 100 * abs(self.factsage_thermo['delta_H_MnO'])
        
        p_heat = (hot_metal_mass * composition['P'] * 0.85 / 100 * 
                 abs(self.factsage_thermo['delta_H_P2O5']) * activity_coeffs['gamma_P'] / 2)
        
        total_reaction_heat = c_heat + si_heat + mn_heat + p_heat
        
        # 2. 动态供氧模型
        oxygen_intensity = self.safe_convert(row['累氧实际'], 5000) / blow_time * 3600
        optimal_oxygen = self.calculate_dynamic_oxygen_demand(composition, temp_k, blow_time)
        oxygen_efficiency = min(1.2, oxygen_intensity / optimal_oxygen)
        
        # 3. 底吹效应
        bottom_effects = self.model_bottom_blowing_effects(composition, oxygen_intensity)
        
        # 4. 脱磷优化
        deP_params = self.optimize_dephosphorization(composition, temp_k)
        
        # 5. 废钢熔化耗热（考虑预热效应）
        scrap_heat = scrap_mass * 1150 * (1 - 0.1 * bottom_effects['heat_transfer_factor'])
        
        # 6. 净反应热（考虑各种效应）
        net_heat = (total_reaction_heat * oxygen_efficiency * 
                   bottom_effects['reaction_rate_factor'] - scrap_heat)
        
        # 7. 温升计算
        total_steel = hot_metal_mass + scrap_mass
        temp_rise = net_heat / (total_steel * self.factsage_thermo['Cp_steel'])
        
        # 8. 热损失（考虑底吹搅拌效应）
        base_loss = 0.6 * blow_time / 60
        stirring_loss_reduction = 0.2 * bottom_effects['stirring_efficiency']
        actual_loss = base_loss * (1 - stirring_loss_reduction)
        
        # 9. 工艺参数修正
        lance_angle = self.safe_convert(row['最大角度'], 15)
        lance_effect = (lance_angle - 15) * 0.8
        
        flux_mass = self.safe_convert(row['石灰'], 4000) + self.safe_convert(row['白云石'], 700)
        flux_effect = (flux_mass - 4700) * 0.002
        
        # 10. 动态控制修正
        temp_deviation = 0  # 实际应用中从历史数据获取
        control_correction = self.dynamic_control['feedback_gain'] * temp_deviation
        
        # 11. 最终温度
        final_temp = (hot_metal_temp + temp_rise - actual_loss + 
                     lance_effect + flux_effect + control_correction)
        
        # 12. 基于脱磷要求的温度约束
        min_temp_for_deP = 1580 + (deP_params['optimal_basicity'] - 2.8) * 20
        final_temp = max(final_temp, min_temp_for_deP)
        
        # 软约束
        if final_temp < 1500:
            final_temp = 1500 + (final_temp - 1500) * 0.2
        elif final_temp > 1850:
            final_temp = 1850 - (final_temp - 1850) * 0.2
        
        return {
            'predicted_temp': final_temp,
            'reaction_heat': total_reaction_heat,
            'oxygen_efficiency': oxygen_efficiency,
            'bottom_effects': bottom_effects,
            'dephosphorization': deP_params,
            'activity_coefficients': activity_coeffs,
            'net_heat': net_heat,
            'temp_rise': temp_rise,
            'heat_loss': actual_loss
        }
    
    def safe_convert(self, value, default=0.0):
        """安全数值转换"""
        if pd.isna(value):
            return default
        try:
            return float(value)
        except:
            return default

def main():
    """主函数：测试FactSage优化集成模型"""
    print("=== 基于FactSage活度计算的高精度集成模型 ===")
    print("结合双渣法脱磷、供氧模型、底吹模型、动态控制\n")
    
    # 读取数据
    try:
        df = pd.read_excel('1-4521剔除重复20250514.xlsx')
        print(f"成功读取训练数据，共{len(df)}条记录")
    except Exception as e:
        print(f"读取数据失败：{e}")
        return
    
    # 初始化模型
    model = FactSageIntegratedModel()
    
    # 测试前100条记录
    results = []
    print("开始FactSage优化模型测试...")
    
    for idx in range(min(100, len(df))):
        row = df.iloc[idx]
        
        try:
            result = model.predict_temperature_with_factsage(row)
            
            actual_temp = model.safe_convert(row['钢水温度'])
            if actual_temp > 0:
                temp_error = abs(result['predicted_temp'] - actual_temp)
                
                record = {
                    '炉号': row['炉号'],
                    '实际温度': actual_temp,
                    '预测温度': result['predicted_temp'],
                    '温度偏差': temp_error,
                    '反应热': result['reaction_heat'],
                    '供氧效率': result['oxygen_efficiency'],
                    '底吹传质增强': result['bottom_effects']['mass_transfer_enhancement'],
                    '最佳碱度': result['dephosphorization']['optimal_basicity'],
                    '脱磷效率': result['dephosphorization']['predicted_efficiency'],
                    'C活度系数': result['activity_coefficients']['gamma_C'],
                    'Si活度系数': result['activity_coefficients']['gamma_Si'],
                    'P活度系数': result['activity_coefficients']['gamma_P']
                }
                results.append(record)
        
        except Exception as e:
            print(f"处理第{idx}行数据时出错：{e}")
            continue
    
    # 分析结果
    if results:
        results_df = pd.DataFrame(results)
        
        print(f"\n=== FactSage优化模型测试结果 ===")
        print(f"测试样本数: {len(results_df)}")
        
        temp_errors = results_df['温度偏差']
        print(f"平均绝对误差: {temp_errors.mean():.1f}°C")
        print(f"标准偏差: {temp_errors.std():.1f}°C")
        print(f"±10°C精度: {(temp_errors <= 10).sum() / len(temp_errors) * 100:.1f}%")
        print(f"±15°C精度: {(temp_errors <= 15).sum() / len(temp_errors) * 100:.1f}%")
        print(f"±20°C精度: {(temp_errors <= 20).sum() / len(temp_errors) * 100:.1f}%")
        
        print(f"\n模型特征分析:")
        print(f"平均供氧效率: {results_df['供氧效率'].mean():.2f}")
        print(f"平均底吹传质增强: {results_df['底吹传质增强'].mean():.2f}")
        print(f"平均最佳碱度: {results_df['最佳碱度'].mean():.2f}")
        print(f"平均脱磷效率: {results_df['脱磷效率'].mean():.1f}%")
        
        # 保存测试结果
        results_df.to_excel('FactSage优化模型测试结果.xlsx', index=False)
        print(f"\n测试结果已保存到: FactSage优化模型测试结果.xlsx")
    
    print("\n=== FactSage优化集成模型测试完成 ===")

if __name__ == "__main__":
    main()
