#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实用高精度温度预测模型
基于数据驱动优化，目标±20°C精度>40%
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

class PracticalTemperaturePredictor:
    """实用高精度温度预测器"""
    
    def __init__(self):
        # 基于实际数据优化的参数
        self.thermal_params = {
            'C_heat': 9800,      # 脱碳反应热 kJ/kg（数据驱动优化）
            'Si_heat': 28500,    # 硅氧化热 kJ/kg
            'Mn_heat': 6800,     # 锰氧化热 kJ/kg
            'P_heat': 22000,     # 磷氧化热 kJ/kg
            'steel_cp': 0.72,    # 钢水热容 kJ/kg·K
            'scrap_heat': 1150   # 废钢熔化热 kJ/kg
        }
        
        # 热损失模型参数（基于实际生产数据拟合）
        self.loss_params = {
            'base_rate': 0.65,       # 基础热损失率 K/min
            'mass_coeff': -0.003,    # 质量系数（大炉次热损失相对较小）
            'time_coeff': 0.08,      # 时间系数
            'temp_coeff': 0.0003     # 温度系数
        }
        
        # 工艺修正系数（基于生产经验）
        self.process_params = {
            'oxygen_coeff': 0.008,    # 供氧强度系数
            'scrap_ratio_coeff': 120, # 废钢比系数
            'flux_coeff': 0.0015,     # 造渣料系数
            'lance_coeff': 0.8        # 枪位系数
        }
        
        # 机器学习模型
        self.ml_model = None
        self.is_trained = False
    
    def safe_convert(self, value, default=0.0):
        """安全数值转换"""
        if pd.isna(value):
            return default
        try:
            return float(value)
        except:
            return default
    
    def extract_features(self, row):
        """提取特征"""
        features = {
            # 基础数据
            'hot_metal_temp': self.safe_convert(row['铁水温度'], 1350),
            'hot_metal_mass': self.safe_convert(row['铁水'], 90),
            'scrap_mass': self.safe_convert(row['废钢'], 20),
            'blow_time': self.safe_convert(row['吹氧时间s'], 600),
            
            # 铁水成分
            'C_content': max(self.safe_convert(row['铁水C'], 4.2), 3.5),
            'Si_content': self.safe_convert(row['铁水SI'], 0.4),
            'Mn_content': self.safe_convert(row['铁水MN'], 0.17),
            'P_content': self.safe_convert(row['铁水P'], 0.13),
            'S_content': self.safe_convert(row['铁水S'], 0.03),
            
            # 工艺参数
            'oxygen_actual': self.safe_convert(row['累氧实际'], 5000),
            'lance_angle': self.safe_convert(row['最大角度'], 15),
            'lime_mass': self.safe_convert(row['石灰'], 4000),
            'dolomite_mass': self.safe_convert(row['白云石'], 700),
            
            # 衍生特征
            'scrap_ratio': 0,
            'total_flux': 0,
            'oxygen_intensity': 0
        }
        
        # 计算衍生特征
        total_metal = features['hot_metal_mass'] + features['scrap_mass']
        if total_metal > 0:
            features['scrap_ratio'] = features['scrap_mass'] / total_metal
        
        features['total_flux'] = features['lime_mass'] + features['dolomite_mass']
        
        if features['blow_time'] > 0:
            features['oxygen_intensity'] = features['oxygen_actual'] / features['blow_time'] * 60
        
        return features
    
    def physics_based_prediction(self, features):
        """基于物理模型的预测"""
        # 计算反应热
        c_oxidized = features['hot_metal_mass'] * features['C_content'] * 0.85 / 100
        si_oxidized = features['hot_metal_mass'] * features['Si_content'] * 0.95 / 100
        mn_oxidized = features['hot_metal_mass'] * features['Mn_content'] * 0.80 / 100
        p_oxidized = features['hot_metal_mass'] * features['P_content'] * 0.85 / 100
        
        total_heat = (c_oxidized * self.thermal_params['C_heat'] +
                     si_oxidized * self.thermal_params['Si_heat'] +
                     mn_oxidized * self.thermal_params['Mn_heat'] +
                     p_oxidized * self.thermal_params['P_heat'])
        
        # 废钢耗热
        scrap_heat = features['scrap_mass'] * self.thermal_params['scrap_heat']
        
        # 净热量
        net_heat = total_heat - scrap_heat
        
        # 温升
        total_steel = features['hot_metal_mass'] + features['scrap_mass']
        temp_rise = net_heat / (total_steel * self.thermal_params['steel_cp'])
        
        # 热损失
        heat_loss = (self.loss_params['base_rate'] * features['blow_time'] / 60 *
                    (1 + self.loss_params['mass_coeff'] * total_steel) *
                    (1 + self.loss_params['time_coeff'] * features['blow_time'] / 600))
        
        # 工艺修正
        oxygen_effect = (features['oxygen_intensity'] - 500) * self.process_params['oxygen_coeff']
        scrap_effect = (features['scrap_ratio'] - 0.2) * self.process_params['scrap_ratio_coeff']
        flux_effect = (features['total_flux'] - 4700) * self.process_params['flux_coeff']
        lance_effect = (features['lance_angle'] - 15) * self.process_params['lance_coeff']
        
        # 最终温度
        final_temp = (features['hot_metal_temp'] + temp_rise - heat_loss +
                     oxygen_effect + scrap_effect + flux_effect + lance_effect)
        
        return final_temp
    
    def train_ml_model(self, df):
        """训练机器学习模型"""
        print("正在训练机器学习模型...")
        
        # 准备训练数据
        X = []
        y = []
        
        for idx, row in df.iterrows():
            if pd.notna(row['钢水温度']):
                features = self.extract_features(row)
                
                # 特征向量
                feature_vector = [
                    features['hot_metal_temp'], features['hot_metal_mass'], features['scrap_mass'],
                    features['blow_time'], features['C_content'], features['Si_content'],
                    features['Mn_content'], features['P_content'], features['oxygen_actual'],
                    features['lance_angle'], features['lime_mass'], features['dolomite_mass'],
                    features['scrap_ratio'], features['total_flux'], features['oxygen_intensity']
                ]
                
                X.append(feature_vector)
                y.append(row['钢水温度'])
        
        X = np.array(X)
        y = np.array(y)
        
        if len(X) > 100:  # 确保有足够的训练数据
            # 分割训练和测试数据
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # 训练随机森林模型
            self.ml_model = RandomForestRegressor(
                n_estimators=100,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            )
            
            self.ml_model.fit(X_train, y_train)
            
            # 评估模型
            y_pred = self.ml_model.predict(X_test)
            mae = mean_absolute_error(y_test, y_pred)
            
            print(f"机器学习模型训练完成，测试集MAE: {mae:.1f}°C")
            self.is_trained = True
        else:
            print("训练数据不足，跳过机器学习模型训练")
    
    def predict_temperature(self, row):
        """预测温度（集成物理模型和机器学习模型）"""
        features = self.extract_features(row)
        
        # 物理模型预测
        physics_pred = self.physics_based_prediction(features)
        
        # 机器学习模型预测
        if self.is_trained:
            feature_vector = np.array([[
                features['hot_metal_temp'], features['hot_metal_mass'], features['scrap_mass'],
                features['blow_time'], features['C_content'], features['Si_content'],
                features['Mn_content'], features['P_content'], features['oxygen_actual'],
                features['lance_angle'], features['lime_mass'], features['dolomite_mass'],
                features['scrap_ratio'], features['total_flux'], features['oxygen_intensity']
            ]])
            
            ml_pred = self.ml_model.predict(feature_vector)[0]
            
            # 集成预测（物理模型权重0.4，机器学习模型权重0.6）
            final_pred = 0.4 * physics_pred + 0.6 * ml_pred
        else:
            final_pred = physics_pred
        
        # 合理性约束
        final_pred = max(1500, min(final_pred, 1850))
        
        return {
            'predicted_temp': final_pred,
            'physics_pred': physics_pred,
            'ml_pred': ml_pred if self.is_trained else None,
            'features': features
        }

def main():
    """主函数"""
    print("=== 实用高精度温度预测模型 ===")
    print("目标：±20°C精度>40%\n")
    
    # 读取数据
    try:
        df = pd.read_excel('1-4521剔除重复20250514.xlsx')
        print(f"成功读取数据，共{len(df)}条记录")
    except Exception as e:
        print(f"读取数据失败：{e}")
        return
    
    # 初始化预测器
    predictor = PracticalTemperaturePredictor()
    
    # 训练机器学习模型
    predictor.train_ml_model(df)
    
    # 预测所有数据
    results = []
    print("开始温度预测...")
    
    for idx, row in df.iterrows():
        if idx % 500 == 0:
            print(f"已处理 {idx}/{len(df)} 条记录")
        
        try:
            result = predictor.predict_temperature(row)
            
            actual_temp = predictor.safe_convert(row['钢水温度'])
            
            record = {
                '炉号': row['炉号'],
                '钢种': row['钢种'],
                '实际温度': actual_temp if actual_temp > 0 else None,
                '预测温度': result['predicted_temp'],
                '物理模型预测': result['physics_pred'],
                'ML模型预测': result['ml_pred'],
                '温度偏差': abs(result['predicted_temp'] - actual_temp) if actual_temp > 0 else None,
                '铁水温度': result['features']['hot_metal_temp'],
                '铁水质量': result['features']['hot_metal_mass'],
                '废钢质量': result['features']['scrap_mass'],
                '吹氧时间': result['features']['blow_time'],
                '废钢比': result['features']['scrap_ratio'],
                '供氧强度': result['features']['oxygen_intensity']
            }
            
            results.append(record)
            
        except Exception as e:
            print(f"处理第{idx}行数据时出错：{e}")
            continue
    
    # 分析结果
    results_df = pd.DataFrame(results)
    temp_valid = results_df.dropna(subset=['温度偏差'])
    
    print(f"\n=== 实用高精度温度预测结果 ===")
    if len(temp_valid) > 0:
        mae = temp_valid['温度偏差'].mean()
        std = temp_valid['温度偏差'].std()
        
        print(f"样本数量: {len(temp_valid)}")
        print(f"平均绝对误差: {mae:.1f}°C")
        print(f"标准偏差: {std:.1f}°C")
        print(f"±10°C精度: {(temp_valid['温度偏差'] <= 10).sum() / len(temp_valid) * 100:.1f}%")
        print(f"±15°C精度: {(temp_valid['温度偏差'] <= 15).sum() / len(temp_valid) * 100:.1f}%")
        print(f"±20°C精度: {(temp_valid['温度偏差'] <= 20).sum() / len(temp_valid) * 100:.1f}%")
        print(f"±30°C精度: {(temp_valid['温度偏差'] <= 30).sum() / len(temp_valid) * 100:.1f}%")
        
        print(f"\n温度预测范围: {results_df['预测温度'].min():.1f} - {results_df['预测温度'].max():.1f}°C")
        print(f"实际温度范围: {temp_valid['实际温度'].min():.1f} - {temp_valid['实际温度'].max():.1f}°C")
        
        # 检查预测范围
        unique_temps = results_df['预测温度'].nunique()
        print(f"预测温度唯一值数量: {unique_temps}")
        
        if (temp_valid['温度偏差'] <= 20).sum() / len(temp_valid) * 100 >= 40:
            print("🎉 成功达到±20°C精度>40%的目标！")
        else:
            print("⚠️ 未达到±20°C精度>40%的目标，需要进一步优化")
    
    # 保存结果
    output_file = '实用高精度温度预测结果.xlsx'
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        results_df.to_excel(writer, sheet_name='预测结果', index=False)
        
        if len(temp_valid) > 0:
            # 精度统计
            accuracy_stats = pd.DataFrame({
                '精度指标': ['样本数量', '平均绝对误差(°C)', '标准偏差(°C)', 
                          '±10°C精度(%)', '±15°C精度(%)', '±20°C精度(%)', '±30°C精度(%)'],
                '数值': [
                    len(temp_valid),
                    temp_valid['温度偏差'].mean(),
                    temp_valid['温度偏差'].std(),
                    (temp_valid['温度偏差'] <= 10).sum() / len(temp_valid) * 100,
                    (temp_valid['温度偏差'] <= 15).sum() / len(temp_valid) * 100,
                    (temp_valid['温度偏差'] <= 20).sum() / len(temp_valid) * 100,
                    (temp_valid['温度偏差'] <= 30).sum() / len(temp_valid) * 100
                ]
            })
            accuracy_stats.to_excel(writer, sheet_name='精度统计', index=False)
    
    print(f"\n结果已保存到: {output_file}")
    print("=== 实用高精度温度预测完成 ===")

if __name__ == "__main__":
    main()
