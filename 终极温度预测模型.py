#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极钢水温度预测模型
完全基于转炉冶金原理重新设计
解决温度预测固定值问题，提高±20°C命中率
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class UltimateTemperaturePredictor:
    """终极温度预测器"""
    
    def __init__(self):
        # 基于实际转炉数据的反应热（kJ/kg）
        self.reaction_heats = {
            'C_oxidation': 11000,    # 脱碳反应热
            'Si_oxidation': 30800,   # 硅氧化热
            'Mn_oxidation': 7200,    # 锰氧化热
            'P_oxidation': 24000,    # 磷氧化热
            'Fe_oxidation': 4800     # 铁氧化热
        }
        
        # 热容数据（kJ/kg·K）
        self.heat_capacities = {
            'steel': 0.75,           # 钢水热容
            'scrap': 0.65,           # 废钢热容
            'slag': 1.2              # 炉渣热容
        }
        
        # 热损失系数（基于实际生产数据拟合）
        self.heat_loss_coeffs = {
            'base_loss_rate': 0.8,   # 基础热损失率 K/min
            'mass_effect': 0.002,    # 质量效应
            'temp_effect': 0.0005,   # 温度效应
            'time_nonlinear': 0.1    # 时间非线性效应
        }
        
        # 工艺参数影响系数（基于生产经验）
        self.process_coeffs = {
            'oxygen_intensity': 0.05,    # 供氧强度影响
            'lance_position': 0.03,      # 枪位影响
            'scrap_ratio': 150,          # 废钢比影响
            'flux_addition': 0.002       # 造渣料影响
        }
    
    def safe_convert(self, value, default=0.0):
        """安全数值转换"""
        if pd.isna(value):
            return default
        try:
            return float(value)
        except:
            return default
    
    def correct_hot_metal_composition(self, row):
        """修正铁水成分"""
        c_content = self.safe_convert(row['铁水C'])
        if c_content < 3.5 or c_content > 5.5:
            c_content = 4.2
        
        return {
            'C': c_content,
            'Si': max(self.safe_convert(row['铁水SI'], 0.4), 0.1),
            'Mn': max(self.safe_convert(row['铁水MN'], 0.17), 0.08),
            'P': max(self.safe_convert(row['铁水P'], 0.13), 0.08),
            'S': max(self.safe_convert(row['铁水S'], 0.03), 0.01)
        }
    
    def calculate_reaction_heat(self, composition, hot_metal_mass):
        """计算反应热"""
        # 各元素氧化量（kg）
        c_oxidized = hot_metal_mass * composition['C'] * 0.85 / 100  # 85%脱碳率
        si_oxidized = hot_metal_mass * composition['Si'] * 0.95 / 100
        mn_oxidized = hot_metal_mass * composition['Mn'] * 0.80 / 100
        p_oxidized = hot_metal_mass * composition['P'] * 0.85 / 100
        fe_oxidized = hot_metal_mass * 0.015  # 1.5%铁损
        
        # 计算各反应热（kJ）
        decarb_heat = c_oxidized * self.reaction_heats['C_oxidation']
        si_heat = si_oxidized * self.reaction_heats['Si_oxidation']
        mn_heat = mn_oxidized * self.reaction_heats['Mn_oxidation']
        p_heat = p_oxidized * self.reaction_heats['P_oxidation']
        fe_heat = fe_oxidized * self.reaction_heats['Fe_oxidation']
        
        total_heat = decarb_heat + si_heat + mn_heat + p_heat + fe_heat
        
        return {
            'total_heat': total_heat,
            'decarb_heat': decarb_heat,
            'si_heat': si_heat,
            'mn_heat': mn_heat,
            'p_heat': p_heat,
            'fe_heat': fe_heat
        }
    
    def calculate_scrap_heat_consumption(self, scrap_mass, target_temp):
        """计算废钢熔化和加热耗热"""
        # 废钢熔化热
        melting_heat = scrap_mass * 1200  # kJ
        
        # 废钢加热到目标温度的热量
        heating_heat = scrap_mass * self.heat_capacities['scrap'] * (target_temp - 25)
        
        return melting_heat + heating_heat
    
    def calculate_heat_losses(self, row, blow_time, steel_mass, avg_temp):
        """计算热损失"""
        # 基础热损失（与时间、质量、温度相关）
        base_loss = (self.heat_loss_coeffs['base_loss_rate'] * blow_time / 60 *
                    (1 + self.heat_loss_coeffs['mass_effect'] * steel_mass) *
                    (1 + self.heat_loss_coeffs['temp_effect'] * avg_temp))
        
        # 时间非线性效应（长时间吹炼热损失增加）
        time_factor = 1 + self.heat_loss_coeffs['time_nonlinear'] * (blow_time / 600 - 1)**2
        
        # 总热损失温度
        total_temp_loss = base_loss * time_factor
        
        return total_temp_loss
    
    def calculate_process_effects(self, row):
        """计算工艺参数对温度的影响"""
        effects = {}
        
        # 1. 供氧强度效应
        oxygen_actual = self.safe_convert(row['累氧实际'], 5000)
        oxygen_standard = 5000  # 标准供氧量
        effects['oxygen'] = (oxygen_actual - oxygen_standard) / 1000 * self.process_coeffs['oxygen_intensity']
        
        # 2. 枪位效应
        lance_angle = self.safe_convert(row['最大角度'], 15)
        lance_standard = 15  # 标准枪位角度
        effects['lance'] = (lance_angle - lance_standard) * self.process_coeffs['lance_position']
        
        # 3. 废钢比效应
        hot_metal = self.safe_convert(row['铁水'], 90)
        scrap = self.safe_convert(row['废钢'], 20)
        scrap_ratio = scrap / (hot_metal + scrap) if (hot_metal + scrap) > 0 else 0.2
        standard_ratio = 0.2
        effects['scrap_ratio'] = (scrap_ratio - standard_ratio) * self.process_coeffs['scrap_ratio']
        
        # 4. 造渣料效应
        lime = self.safe_convert(row['石灰'], 4000)
        dolomite = self.safe_convert(row['白云石'], 700)
        flux_total = lime + dolomite
        flux_standard = 4700
        effects['flux'] = (flux_total - flux_standard) * self.process_coeffs['flux_addition']
        
        # 5. 钢种效应
        steel_grade = str(row.get('钢种', 'Q355D'))
        if 'Q235' in steel_grade:
            effects['grade'] = -15
        elif 'Q355' in steel_grade:
            effects['grade'] = 0
        elif 'Q420' in steel_grade or 'Q460' in steel_grade:
            effects['grade'] = 10
        else:
            effects['grade'] = 0
        
        return effects
    
    def predict_temperature(self, row):
        """预测钢水温度"""
        # 基础数据
        hot_metal_temp = self.safe_convert(row['铁水温度'], 1350)
        hot_metal_mass = self.safe_convert(row['铁水'], 90)
        scrap_mass = self.safe_convert(row['废钢'], 20)
        blow_time = self.safe_convert(row['吹氧时间s'], 600)
        
        # 修正铁水成分
        composition = self.correct_hot_metal_composition(row)
        
        # 计算反应热
        reaction_heat_data = self.calculate_reaction_heat(composition, hot_metal_mass)
        total_reaction_heat = reaction_heat_data['total_heat']
        
        # 总钢水质量
        total_steel = hot_metal_mass + scrap_mass
        
        # 初步温度估算（用于废钢耗热计算）
        preliminary_temp = hot_metal_temp + total_reaction_heat / (total_steel * self.heat_capacities['steel'])
        
        # 废钢熔化和加热耗热
        scrap_heat_consumption = self.calculate_scrap_heat_consumption(scrap_mass, preliminary_temp)
        
        # 净反应热
        net_heat = total_reaction_heat - scrap_heat_consumption
        
        # 温升计算
        temp_rise = net_heat / (total_steel * self.heat_capacities['steel'])
        
        # 中间温度
        intermediate_temp = hot_metal_temp + temp_rise
        
        # 热损失计算
        heat_loss_temp = self.calculate_heat_losses(row, blow_time, total_steel, intermediate_temp)
        
        # 工艺参数修正
        process_effects = self.calculate_process_effects(row)
        total_process_effect = sum(process_effects.values())
        
        # 最终温度
        final_temp = intermediate_temp - heat_loss_temp + total_process_effect
        
        # 合理性检查（软约束）
        if final_temp < 1500:
            final_temp = 1500 + (final_temp - 1500) * 0.3
        elif final_temp > 1900:
            final_temp = 1900 - (final_temp - 1900) * 0.3
        
        return {
            'predicted_temp': final_temp,
            'hot_metal_temp': hot_metal_temp,
            'temp_rise': temp_rise,
            'heat_loss': heat_loss_temp,
            'process_effect': total_process_effect,
            'reaction_heat': total_reaction_heat,
            'scrap_consumption': scrap_heat_consumption,
            'composition': composition,
            'process_effects': process_effects
        }

def integrate_with_slag_prediction():
    """集成炉渣预测和温度预测"""
    print("=== 集成炉渣成分预测和终极温度预测系统 ===")
    print("解决温度预测固定值问题，提高±20°C命中率\n")
    
    # 读取数据
    try:
        df = pd.read_excel('1-4521剔除重复20250514.xlsx')
        print(f"成功读取数据，共{len(df)}条记录")
    except Exception as e:
        print(f"读取数据失败：{e}")
        return
    
    # 读取炉渣预测结果
    try:
        slag_df = pd.read_excel('最终优化版炉渣预测结果.xlsx', sheet_name='最终预测结果')
        print(f"成功读取炉渣预测结果，共{len(slag_df)}条记录")
    except Exception as e:
        print(f"读取炉渣预测结果失败：{e}")
        return
    
    # 初始化温度预测器
    temp_predictor = UltimateTemperaturePredictor()
    
    # 预测结果
    results = []
    
    print("开始集成预测...")
    for idx, row in df.iterrows():
        if idx % 500 == 0:
            print(f"已处理 {idx}/{len(df)} 条记录")
        
        try:
            # 温度预测
            temp_result = temp_predictor.predict_temperature(row)
            
            # 获取对应的炉渣预测结果
            slag_result = None
            if idx < len(slag_df):
                slag_result = slag_df.iloc[idx]
            
            # 保存结果
            result = {
                '炉号': row['炉号'],
                '钢种': row['钢种'],
                '实际温度': row['钢水温度'] if pd.notna(row['钢水温度']) else None,
                '预测温度': temp_result['predicted_temp'],
                '温度偏差': abs(temp_result['predicted_temp'] - row['钢水温度']) if pd.notna(row['钢水温度']) else None,
                '铁水温度': temp_result['hot_metal_temp'],
                '温升': temp_result['temp_rise'],
                '热损失': temp_result['heat_loss'],
                '工艺修正': temp_result['process_effect'],
                '反应热': temp_result['reaction_heat'],
                '废钢耗热': temp_result['scrap_consumption'],
                '修正后C含量': temp_result['composition']['C'],
                '修正后Si含量': temp_result['composition']['Si'],
                '修正后Mn含量': temp_result['composition']['Mn'],
                '修正后P含量': temp_result['composition']['P']
            }
            
            # 添加炉渣预测结果
            if slag_result is not None:
                result.update({
                    'CaO预测': slag_result.get('CaO预测', 45),
                    'SiO2预测': slag_result.get('SiO2预测', 16),
                    'FeO预测': slag_result.get('FeO预测', 20),
                    'MgO预测': slag_result.get('MgO预测', 8),
                    'MnO预测': slag_result.get('MnO预测', 6),
                    'P2O5预测': slag_result.get('P2O5预测', 3),
                    '预测碱度': slag_result.get('预测碱度', 2.8)
                })
            
            results.append(result)
            
        except Exception as e:
            print(f"处理第{idx}行数据时出错：{e}")
            continue
    
    # 分析结果
    results_df = pd.DataFrame(results)
    
    # 温度预测精度分析
    temp_valid = results_df.dropna(subset=['温度偏差'])
    
    print(f"\n=== 终极温度预测精度分析 ===")
    if len(temp_valid) > 0:
        print(f"温度预测样本数: {len(temp_valid)}")
        print(f"平均绝对误差: {temp_valid['温度偏差'].mean():.1f}°C")
        print(f"标准偏差: {temp_valid['温度偏差'].std():.1f}°C")
        print(f"±10°C精度: {(temp_valid['温度偏差'] <= 10).sum() / len(temp_valid) * 100:.1f}%")
        print(f"±15°C精度: {(temp_valid['温度偏差'] <= 15).sum() / len(temp_valid) * 100:.1f}%")
        print(f"±20°C精度: {(temp_valid['温度偏差'] <= 20).sum() / len(temp_valid) * 100:.1f}%")
        print(f"±30°C精度: {(temp_valid['温度偏差'] <= 30).sum() / len(temp_valid) * 100:.1f}%")
        
        print(f"\n温度预测范围: {results_df['预测温度'].min():.1f} - {results_df['预测温度'].max():.1f}°C")
        print(f"实际温度范围: {temp_valid['实际温度'].min():.1f} - {temp_valid['实际温度'].max():.1f}°C")
        
        # 检查是否还有固定值问题
        unique_temps = results_df['预测温度'].nunique()
        print(f"预测温度唯一值数量: {unique_temps}")
        if unique_temps < 10:
            print("⚠️ 警告：预测温度变化范围仍然过小")
        else:
            print("✅ 预测温度变化范围正常")
    
    # 保存最终结果
    output_file = '终极集成预测结果.xlsx'
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        results_df.to_excel(writer, sheet_name='集成预测结果', index=False)
        
        # 温度预测精度统计
        if len(temp_valid) > 0:
            temp_stats = {
                '指标': ['样本数量', '平均绝对误差(°C)', '标准偏差(°C)', '±10°C精度(%)', '±15°C精度(%)', '±20°C精度(%)', '±30°C精度(%)'],
                '数值': [
                    len(temp_valid),
                    temp_valid['温度偏差'].mean(),
                    temp_valid['温度偏差'].std(),
                    (temp_valid['温度偏差'] <= 10).sum() / len(temp_valid) * 100,
                    (temp_valid['温度偏差'] <= 15).sum() / len(temp_valid) * 100,
                    (temp_valid['温度偏差'] <= 20).sum() / len(temp_valid) * 100,
                    (temp_valid['温度偏差'] <= 30).sum() / len(temp_valid) * 100
                ]
            }
            temp_stats_df = pd.DataFrame(temp_stats)
            temp_stats_df.to_excel(writer, sheet_name='温度预测精度', index=False)
    
    print(f"\n终极集成预测结果已保存到: {output_file}")
    print("=== 终极温度预测系统运行完成 ===")
    
    return results_df

if __name__ == "__main__":
    results = integrate_with_slag_prediction()
